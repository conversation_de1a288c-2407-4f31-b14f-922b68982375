import { APIEvent } from "@solidjs/start/server";
import { basicAuthConfig } from "~/configs/server-config";
import { findOption } from "~/services/db-services/option-db-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	"use server";

	const accessKey = basicAuthConfig().accessKey;
	const secretKey = basicAuthConfig().secretKey;

	// HTTP Basic Authorization check
	const authHeader = request.headers.get("Authorization");

	if (!authHeader || !authHeader.startsWith("Basic ")) {
		return makeJsonResponse(
			{
				success: false,
				message: "Missing or invalid Authorization header",
			},
			401,
		);
	}

	try {
		// Extract and decode the base64 credentials
		const base64Credentials = authHeader.substring("Basic ".length);
		const credentials = atob(base64Credentials);
		const [providedAccessKey, providedSecretKey] = credentials.split(":");

		// Verify credentials
		if (providedAccessKey !== accessKey || providedSecretKey !== secretKey) {
			return makeJsonResponse(
				{
					success: false,
					message: "Invalid credentials",
				},
				401,
			);
		}
	} catch (error) {
		return makeJsonResponse(
			{
				success: false,
				message: "Invalid Authorization header format",
			},
			401,
		);
	}

	const name = params.name;

	if (!name) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option name is required",
			},
			400,
		);
	}

	const option = await findOption(name);

	if (option === null) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option not found",
			},
			404,
		);
	}

	return makeJsonResponse({
		success: true,
		message: "Option retrieved successfully",
		data: option,
	});
}

import { APIEvent } from "@solidjs/start/server";
import { findOption } from "~/services/db-services/option-db-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	const name = params.name;

	if (!name) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option name is required",
			},
			400,
		);
	}

	const option = await findOption(name);

	if (option === null) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option not found",
			},
			404,
		);
	}

	return makeJsonResponse({
		success: true,
		message: "Option retrieved successfully",
		data: option,
	});
}

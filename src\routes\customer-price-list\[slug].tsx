import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { ErrorBoundary, Show, Suspense } from "solid-js";
import CustomerPriceListTable from "~/components/customer-price-list/CustomerPriceListTable";
import ErrorMessage from "~/components/error/ErrorMessage";
import LoadingSpinner from "~/components/LoadingSpinner";
import { CustomProductData } from "~/types/dto";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { fetchCustomProductData } from "~/services/http-services/custom-product-data-service";
import { getCustomerPriceListPageData } from "~/services/http-services/page-service";
import CustomerPriceListTableLoading from "~/components/customer-price-list/CustomerPriceListTableLoading";

const getPageData = query(async (slug: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const pageDataResponse = await getCustomerPriceListPageData(slug);

	if (!pageDataResponse.success || !pageDataResponse.data) {
		return pageDataResponse;
	}

	const customerCustomProductDataResponse = await fetchCustomProductData({
		customerId: pageDataResponse.data.customerPricingScheme.customerId,
		perPage: 1000,
	});

	if (
		!customerCustomProductDataResponse.success ||
		!customerCustomProductDataResponse.data
	) {
		pageDataResponse.success = false;
		pageDataResponse.message = customerCustomProductDataResponse.message;

		return pageDataResponse;
	}

	const customProductDataCollection: Record<string, CustomProductData> = {};

	for (const customProductData of customerCustomProductDataResponse.data) {
		customProductDataCollection[customProductData.productId] =
			customProductData;
	}

	pageDataResponse.data.customProductDataCollection =
		customProductDataCollection;

	return pageDataResponse;
}, "customerPriceListPageData");

// DO NOT PRELOAD THIS PAGE
// export const route = {
// 	preload: ({ params }) => getPageData(params.slug),
// } satisfies RouteDefinition;

export default function CustomerPriceListPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.slug), {
		deferStream: true,
	});

	return (
		<ErrorBoundary
			fallback={
				<ErrorMessage
					title="Ooops!"
					message={"Something went wrong, please try again later."}
					isFullPage={true}
					useButtons={{
						backButton: true,
						reloadButton: true,
					}}
				/>
			}
		>
			<Suspense fallback={<CustomerPriceListTableLoading />}>
				<Show
					when={pageData()?.success && pageData()?.data}
					fallback={
						<ErrorMessage
							title="Ooops!"
							message={
								pageData()?.message ??
								"Failed to load customer's price list, please try again later."
							}
							isFullPage={true}
							useButtons={{
								backButton: true,
								reloadButton: true,
							}}
						/>
					}
				>
					<div class="relative mx-auto w-[1800px] max-w-[1800px] px-6 py-14 lg:px-14 2xl:w-auto print:w-auto print:min-w-auto print:px-0 print:py-0 print:pt-10">
						<h2 class="relative flex items-center justify-between">
							<span class="font-display relative block w-10/12 text-center text-3xl font-bold text-wrap text-[#656565]">
								Price List
								<br />
								{pageData()?.data?.customerPricingScheme?.customerName ?? ""}
							</span>

							<span class="relative block w-2/12 text-center">
								<img
									src="/images/legacy-home-indonesia-logo.png"
									alt="Logo"
									class="mx-auto h-20 w-auto max-w-full"
								/>
							</span>
						</h2>

						<CustomerPriceListTable
							customerPricingScheme={pageData()?.data?.customerPricingScheme}
							customProductDataCollection={
								pageData()?.data?.customProductDataCollection ?? {}
							}
							prices={pageData()?.data?.prices ?? []}
							productsFobPrice={pageData()?.data?.productsFobPrice ?? {}}
							issuedDate={pageData()?.data?.issuedDate ?? ""}
						/>
					</div>
				</Show>
			</Suspense>
		</ErrorBoundary>
	);
}
